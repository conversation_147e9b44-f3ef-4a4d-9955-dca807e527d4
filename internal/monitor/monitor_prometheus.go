package monitor

import "github.com/prometheus/client_golang/prometheus"

var (
	DayLoginUserTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "day_login_user_total",
		Help: "登录数",
	},
		[]string{"product", "id"})

	RegisterUserTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
		Name: "register_user_total",
		Help: "注册数",
	},
		[]string{"product", "id"})
)

func init() {
	prometheus.MustRegister(DayLoginUserTotal, RegisterUserTotal)
}
