package proc

import (
	"context"
	"loginsrv/internal/repo/rpc_user"
	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/login"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
)

type LoginService struct {
}

var (
	once     = &sync.Once{}
	instance *LoginService
)

func GetLoginServiceInstance() *LoginService {
	if instance != nil {
		return instance
	}

	once.Do(func() {
		instance = &LoginService{}
	})
	return instance
}

func (l *LoginService) DeleteAccountReq(ctx context.Context, req *loginPB.DeleteAccountReq) *loginPB.DeleteAccountRsp {
	entry := logx.NewLogEntry(ctx)
	playerId := interceptor.GetRPCOptions(ctx).PlayerId

	rsp := &loginPB.DeleteAccountRsp{Ret: protox.FillCodeResult(commonPB.ErrCode_ERR_FAIL)}

	// RPC 删除账号
	ret, err := rpc_user.RpcDeleteAccount(ctx, req.GetProductId(), playerId)
	if err != nil || ret.Code != commonPB.ErrCode_ERR_SUCCESS {
		entry.Errorf("delete account:%d failed, err:%v", playerId, err)
		rsp.Ret = ret
		return rsp
	}

	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	entry.Infof("delete product:%d account:%d success", req.GetProductId(), playerId)

	return rsp
}
