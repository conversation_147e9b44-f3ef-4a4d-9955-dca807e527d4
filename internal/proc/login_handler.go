package proc

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/login"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/transport"
	intranetGrpc "git.keepfancy.xyz/back-end/frameworks/kit/transport/gaterpc"
)

type LoginHandler struct {
}

// 登录服实例
func GetLoginInstance() *LoginHandler {
	return &LoginHandler{}
}

func (l *LoginHandler) DeleteAccountReq(ctx context.Context, _ *intranetGrpc.Header, req *loginPB.DeleteAccountReq) *transport.ResponseMsg {
	rsp := GetLoginServiceInstance().DeleteAccountReq(ctx, req)
	return protox.PackRespMsg(commonPB.MsgID_CMD_DELETE_ACCOUNT_RSP, rsp)
}