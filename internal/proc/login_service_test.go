package proc

import (
	"context"
	"loginsrv/internal/repo/rpc_user"
	"testing"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_mysql"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"github.com/spf13/viper"
)

func init() {
	viper.SetDefault("consul_addr", "************:8500")
	viper.SetDefault("kafka_url", "************:9092")
	viper.SetDefault("nsqd_addr", "************:4150")

	conf := map[string]string{
		"addr":   "************:6379",
		"passwd": "8888",
	}
	viper.SetDefault("redis_list", map[string]interface{}{
		dict_redis.RDBGame:   conf,
		dict_redis.RDBPlayer: conf,
	})

	db := dict_mysql.MysqlDBGeneral
	dbConf := map[string]interface{}{
		"addr":   "************:3306",
		//"addr":   "localhost:3306",
		"passwd": "fancydb2024#",
		"user":   "root",
		"db":     db,
	}

	viper.SetDefault(dict.ConfigMysqlList, map[string]interface{}{
		db: dbConf,
	})
}

func TestLoginService_DeleteAccountReq(t *testing.T) {
	rsp, err := rpc_user.RpcDeleteAccount(context.Background(), int32(1), 191)
	t.Logf("%+v, ret:%+v", rsp,err)
}
