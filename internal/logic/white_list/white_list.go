package whiteList

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
)

// IsInLoginWhiteList 是否在登录白名单
func IsInLoginWhiteList(ctx context.Context, playerId uint64, req *loginRpc.LoginReq) bool {
	if playerId == 0 || req == nil {
		return false
	}

	// 是否开启
	blockConf := cmodel.GetBlockStrategy(consul_config.WithChannel(int32(req.GetChannelId())), consul_config.WithProduct(int(req.GetProductId())))
	// 没有配置或者不在开启的类型中
	if blockConf == nil || !transform.Int32SliceContain(blockConf.WhiteOpenType, int32(commonPB.WHITE_FUNC_TYPE_WFT_LOGIN)) {
		return false
	}

	whiteConf := cmodel.GetAllWhiteList(consul_config.WithChannel(int32(req.GetChannelId())), consul_config.WithProduct(int(req.GetProductId())))
	for _, v := range whiteConf {
		if v.WhiteType == int32(commonPB.WHITE_FUNC_TYPE_WFT_LOGIN) && uint64(v.Uid) == playerId {
			return true
		}
	}

	return false
}
