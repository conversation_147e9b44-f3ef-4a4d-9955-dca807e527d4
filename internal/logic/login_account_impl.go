package logic

import (
	"context"
	"errors"
	"loginsrv/internal/repo/rpc_user"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/lib/crypto"
	"git.keepfancy.xyz/back-end/frameworks/lib/utility"
)

// AccountLogin 账号密码登录实现
type AccountLogin struct {
	*BaseLogin
}

// NewAccountLogin 创建账号密码登录实例
func NewAccountLogin() *AccountLogin {
	return &AccountLogin{
		BaseLogin: NewBaseLogin(commonPB.LOGIN_TYPE_LT_PASSWORD),
	}
}

// validateRequest 验证请求参数
func (l *AccountLogin) validateRequest(req *loginRpc.LoginReq) error {
	if err := l.ValidateBaseParams(req); err != nil {
		return err
	}

	accountInfo := req.GetAccountInfo()
	if accountInfo == nil {
		return errors.New("account info must")
	}

	account := accountInfo.GetAccount()
	password := accountInfo.GetPassword()

	// 检查账号
	if !utility.CheckAccountPwd(account) {
		return errors.New("account must be 6-16 characters")
	}

	// 检查密码
	if !utility.CheckAccountPwd(password) {
		return errors.New("password must be 6-16 characters")
	}

	return nil
}

// Login 账号密码登录流程
func (l *AccountLogin) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{Ret: protox.DefaultResult()}
	rsp.Ret.Desc += "account login failed"

	// 验证请求参数
	if err := l.validateRequest(req); err != nil {
		entry.Error(err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		rsp.Ret.Desc += " : " + err.Error()
		return rsp, err
	}

	accountInfo := req.GetAccountInfo()
	productId := req.GetProductId()
	account := accountInfo.GetAccount()
	password := accountInfo.GetPassword()

	// 密码转化为hash值 (加密保存)
	hashPwd := crypto.HashString(password)
	accountInfo.Password = hashPwd

	// 根据账号查询玩家信息
	playerId, err := rpc_user.RpcGetPlayerIdByAccount(ctx, productId, account)
	if err != nil {
		entry.Errorf("RPC query account:%+v err:%+v", accountInfo, err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE)
		rsp.Ret.Desc += " : get account data error"
		return rsp, errors.New("login failed")
	}

	entry.Infof("账号登录:%+v - playerId : %d", account, playerId)

	// 如果是登录而非注册，且用户存在，则需要校验密码
	if !req.GetIsReg() && playerId > 0 {
		// 校验账号密码
		validPlayerId, err := rpc_user.RpcGetPlayerIdByAccountAndPwd(ctx, productId, accountInfo)
		if validPlayerId <= 0 || err != nil {
			entry.Errorf("account:%+v password:%+v player:%d, err:%v", account, password, validPlayerId, err)
			rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_ACCOUNT_PASSWORD_ERROR)
			return rsp, errors.New("account or password error")
		}
		// 确保使用验证后的playerId
		playerId = validPlayerId
	}

	// 处理登录流程
	return l.DefaultLoginProcess(ctx, req, playerId)
}
