package logic

import (
	"context"
	"errors"
	"git.keepfancy.xyz/back-end/frameworks/kit/location"
	"loginsrv/internal/dao"
	"loginsrv/internal/repo"
	"loginsrv/internal/repo/rpc_user"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"
)

// BaseLogin 基础登录类，封装登录逻辑
type BaseLogin struct {
	LoginType commonPB.LOGIN_TYPE
	Hooks     LoginHooks
}

// NewBaseLogin 创建基础登录实例
func NewBaseLogin(loginType commonPB.LOGIN_TYPE) *BaseLogin {
	return &BaseLogin{
		LoginType: loginType,
		Hooks:     &DefaultLoginHooks{},
	}
}

// SetHooks 设置登录钩子
func (b *BaseLogin) SetHooks(hooks LoginHooks) {
	if hooks != nil {
		b.Hooks = hooks
	}
}

// ValidateBaseParams 验证基本参数
func (b *BaseLogin) ValidateBaseParams(req *loginRpc.LoginReq) error {
	if req == nil {
		return errors.New("request is nil")
	}
	if req.GetProductId() == 0 {
		return errors.New("product id is required")
	}
	return nil
}

// HandleRegistration 处理注册逻辑
func (b *BaseLogin) HandleRegistration(ctx context.Context, req *loginRpc.LoginReq, playerId uint64) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{Ret: protox.DefaultResult()}

	if playerId > 0 {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_ACCOUNT_IS_EXIST, "account already exists")
		return rsp, errors.New("account already exists")
	}

	// 查询城市
	var region commonPB.RegionInfo
	locationInfo := location.GetLocationByIP(req.GetDeviceInfo().GetClientIp())
	if locationInfo == nil {
		entry.Errorf("location info is nil playerId=%d", playerId)
	} else {
		region = commonPB.RegionInfo{
			Country: locationInfo.Country,
			City:    locationInfo.City,
		}
	}

	// RPC 创建账户
	rpcRsp, errRpc := rpc_user.RpcCreatePlayer(ctx, req, &region)
	if errRpc != nil {
		entry.Errorf("rpc create call fail :%v", errRpc)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, errRpc.Error())
		return rsp, errRpc
	}

	rsp.PlayerId = rpcRsp.PlayerId
	rsp.IsReg = true
	rsp.RichUserInfo = rpcRsp.RichUserInfo
	// 上次登录设备标识
	rsp.OriIdentifier = rpcRsp.RichUserInfo.BriefUserInfo.GetLastDeviceCode()

	return rsp, nil
}

// HandleLogin 处理登录逻辑
func (b *BaseLogin) HandleLogin(ctx context.Context, req *loginRpc.LoginReq, playerId uint64) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{Ret: protox.DefaultResult()}

	if playerId == 0 {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_ACCOUNT_NOT_EXIST, "account not exists")
		return rsp, errors.New("account not exists")
	}

	// 登录返回
	rpcRsp, errRpc := crpc_user.RpcGetPlayerInfo(ctx, req.GetProductId(), playerId)
	if errRpc != nil {
		entry.Errorf("rpc get player info fail :%v", errRpc)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_OPERATION, errRpc.Error())
		return rsp, errRpc
	}

	rsp.PlayerId = rpcRsp.PlayerId
	rsp.IsReg = false
	rsp.RichUserInfo = rpcRsp.RichUserInfo
	// 上次登录设备标识
	rsp.OriIdentifier = rpcRsp.RichUserInfo.BriefUserInfo.GetLastDeviceCode()

	return rsp, nil
}

// CreateToken 创建并存储token
func (b *BaseLogin) CreateToken(ctx context.Context, playerId uint64) (string, error) {
	entry := logx.NewLogEntry(ctx)
	token := repo.GenerateToken(playerId)
	if errCache := dao.DefaultLoginTokenCache.Create(playerId, token); errCache != nil {
		entry.Errorf("create token failed [playerID:%d err: %v]", playerId, errCache)
		return "", errCache
	}
	return token, nil
}

// DefaultLoginProcess 默认登录流程处理
func (b *BaseLogin) DefaultLoginProcess(ctx context.Context, req *loginRpc.LoginReq, playerId uint64) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	var rsp *loginRpc.LoginRsp

	// 执行钩子：登录前
	errCode, err := b.Hooks.BeforeLogin(ctx, playerId, req)
	if err != nil {
		rsp = &loginRpc.LoginRsp{Ret: protox.FillCodeResult(errCode, err.Error())}
		return rsp, err
	}

	// 注册或登录处理
	if req.GetIsReg() {
		rsp, err = b.HandleRegistration(ctx, req, playerId)
	} else {
		rsp, err = b.HandleLogin(ctx, req, playerId)
	}
	if err != nil {
		return rsp, err
	}

	// 创建token
	token, err := b.CreateToken(ctx, rsp.PlayerId)
	if err != nil {
		return rsp, err
	}
	rsp.Token = token
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	// 执行钩子：登录后
	errCode, err = b.Hooks.AfterLogin(ctx, req, rsp)
	if err != nil {
		rsp.Ret = protox.FillCodeResult(errCode, err.Error())
		return rsp, err
	}

	entry.Infof("%s login success - playerId: %d, isReg: %v", b.LoginType, rsp.PlayerId, rsp.IsReg)
	return rsp, nil
}
