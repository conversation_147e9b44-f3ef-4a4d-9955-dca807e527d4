package logicAnti

import (
	"context"
	"errors"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
)

// CheckRealNameAndAntiAddiction 检查实名认证和防沉迷状态
// 此函数会根据检查结果修改rsp对象，并返回可能的错误
func CheckRealNameAndAntiAddiction(ctx context.Context, req *loginRpc.LoginReq, rsp *loginRpc.LoginRsp) error {
	entry := logx.NewLogEntry(ctx)

	// 获取实名认证配置
	realNameConf := cmodel.GetRealNameAuth(
		consul_config.WithProduct(int(req.GetProductId())),
		consul_config.WithChannel(int32(req.GetChannelId())),
	)

	// 判断是否需要实名认证
	if realNameConf == nil || !realNameConf.Enable {
		return nil
	}

	// 检查是否已实名认证
	isRealNameAuth := rsp.RichUserInfo != nil && rsp.RichUserInfo.RealNameAuth
	playerId := rsp.PlayerId
	productId := req.GetProductId()

	if isRealNameAuth {
		// 如果已经实名认证了，判断是否存在防沉迷
		// 根据实名认证信息判断玩家是否可登录
		if AntiAddictionLogin(ctx, productId, playerId) {
			// 防沉迷状态
			rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_ANTI_ADDICTION)
			return errors.New("player blocked by anti-addiction system")
		}

		// 是否在防沉迷中
		if IsInAntiAddiction(ctx, productId, playerId) {
			rsp.IsInAntiAddiction = true
			entry.Infof("playerId:%+v in anti addiction", playerId)
		}
	} else {
		// 未实名认证，标记需要强制实名
		rsp.ForceRealName = true
	}

	return nil
}
