package logicAnti

import (
	"context"
	rpc_hall "loginsrv/internal/repo/prc_hall"
	"loginsrv/internal/repo/rpc_user"
	"time"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// AntiAddictionLogin 校验防沉迷登录 返回是否在防沉迷状态中
func AntiAddictionLogin(ctx context.Context, productId int32, playerId uint64) bool {
	entry := logx.NewLogEntry(ctx)
	// 未成年工作日不可登录游戏
	if IsInAntiAddiction(ctx, productId, playerId) {
		// 查询防沉迷时间
		holidayType := rpc_hall.RpcQueryHolidayInfo(ctx)
		if holidayType <= commonPB.HOLIDAY_TYPE_HT_WORKING {
			entry.Debugf("holiday type:%+v can not login", holidayType)
			return true
		} else {
			// 节假日 只有20-21点才可登录
			nowTime := time.Now()
			if nowTime.Hour() == 20 {
				return false
			} else {
				entry.Debugf("holiday type:%+v time can not login", holidayType)
				return true
			}
		}
	} else {
		return false
	}
}

func IsInAntiAddiction(ctx context.Context, productId int32, playerId uint64) bool {
	// 查询玩家年龄信息
	entry := logx.NewLogEntry(ctx)
	ageType, err := rpc_user.RpcQueryPlayerAgeInfo(ctx, productId, playerId)
	if ageType == commonPB.USER_AGE_UA_UNKNOWN || err != nil {
		entry.Errorf("query player:%d age info failed:%+v", playerId, err)
		return false
	}

	entry.Debugf("query player:%d age info success : %v", playerId, ageType)

	return ageType <= commonPB.USER_AGE_UA_YOUNG
}
