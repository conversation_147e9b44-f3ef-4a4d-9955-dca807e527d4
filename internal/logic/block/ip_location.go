package block

import (
	"context"

	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/consul_config"
	"git.keepfancy.xyz/back-end/frameworks/kit/location"
)

// IsIpBlock 判断是否ip封杀
func IsIpBlock(ctx context.Context, req *loginRpc.LoginReq) bool {
	entry := logx.NewLogEntry(ctx)

	if req == nil || req.GetDeviceInfo() == nil {
		return false
	}

	// 是否开启
	blockConf := cmodel.GetBlockStrategy(consul_config.WithChannel(int32(req.GetChannelId())), consul_config.WithProduct(int(req.GetProductId())))
	if blockConf == nil || !blockConf.IpBlockEnable {
		return false
	}

	// 查询ip位置
	locationInfo := location.GetLocationByIP(req.GetDeviceInfo().GetClientIp())
	if locationInfo == nil {
		return false
	}

	locationConf := cmodel.GetAllLocationBlock(consul_config.WithChannel(int32(req.GetChannelId())), consul_config.WithProduct(int(req.GetProductId())))
	for _, v := range locationConf {
		// 先校验类型和国家是否匹配
		if v.LocationType == int32(commonPB.LOCATION_TYPE_LT_FORBID) && v.Country == locationInfo.Country {
			// 如果配置了城市，则需要城市也匹配；否则只匹配国家即可
			if v.City == "" || v.City == locationInfo.City {
				entry.Infof("ip block: country=%s, city=%s id=%d", v.Country, v.City, v.Id)
				return true
			}
		}
	}

	return false
}
