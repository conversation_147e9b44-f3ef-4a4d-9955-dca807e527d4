package block

import (
	"context"

	whiteList "loginsrv/internal/logic/white_list"

	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
)

// CheckLoginBlock 检查是否登录封杀
func CheckLoginBlock(ctx context.Context, playerId uint64, req *loginRpc.LoginReq) bool {
	if req == nil {
		return false
	}
	entry := logx.NewLogEntry(ctx)
	// 玩家在白名单中 跳过封杀
	if whiteList.IsInLoginWhiteList(ctx, playerId, req) {
		entry.Debugf("playerId:%+v in login white list", playerId)
		return false
	}

	// IP被封杀
	return IsIpBlock(ctx, req)
}
