package logic

import (
	"context"
	"errors"
	"loginsrv/internal/repo/rpc_user"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// VisitorLogin 游客登录实现
type VisitorLogin struct {
	*BaseLogin
}

// NewVisitorLogin 创建游客登录实例
func NewVisitorLogin() *VisitorLogin {
	return &VisitorLogin{
		BaseLogin: NewBaseLogin(commonPB.LOGIN_TYPE_LT_VISITOR),
	}
}

// validateRequest 验证请求参数
func (l *VisitorLogin) validateRequest(req *loginRpc.LoginReq) error {
	if err := l.ValidateBaseParams(req); err != nil {
		return err
	}

	deviceCode := req.GetDeviceInfo().GetDeviceCode()
	if deviceCode == "" {
		return errors.New("device code is required")
	}

	return nil
}

// Login 游客登录流程
func (l *VisitorLogin) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{Ret: protox.DefaultResult()}
	rsp.Ret.Desc += "visitor login failed"

	// 验证请求参数
	if err := l.validateRequest(req); err != nil {
		entry.Error(err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		rsp.Ret.Desc += " " + err.Error()
		return rsp, err
	}

	// 根据设备码获取用户信息
	deviceCode := req.GetDeviceInfo().GetDeviceCode()
	productID := req.GetProductId()
	playerID, err := rpc_user.RpcGetUserInfoByDeviceCode(ctx, deviceCode, productID, req.GetLoginType())
	if err != nil {
		entry.Errorf("RPC获取游客态玩家数据失败%s", deviceCode)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE)
		rsp.Ret.Desc += " : get visitor data error"
		return rsp, errors.New("login failed")
	}

	entry.Infof("游客登录 - playerID : %d", playerID)

	req.IsReg = playerID == 0

	// 处理登录流程
	return l.DefaultLoginProcess(ctx, req, playerID)
}
