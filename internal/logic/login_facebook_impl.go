package logic

import (
	"context"
	"errors"
	"loginsrv/internal/repo/rpc_user"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/sdk"
)

// FacebookLogin Facebook登录实现
type FacebookLogin struct {
	*BaseLogin
}

// NewFacebookLogin 创建Facebook登录实例
func NewFacebookLogin() *FacebookLogin {
	return &FacebookLogin{
		BaseLogin: NewBaseLogin(commonPB.LOGIN_TYPE_LT_FACEBOOK),
	}
}

// validateRequest 验证请求参数
func (l *FacebookLogin) validateRequest(req *loginRpc.LoginReq) error {
	if err := l.ValidateBaseParams(req); err != nil {
		return err
	}

	if req.GetThirdInfo() == nil || req.GetThirdInfo().GetToken() == "" {
		return errors.New("third info or token is empty")
	}

	return nil
}

// Login Facebook登录流程
func (l *FacebookLogin) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{Ret: protox.DefaultResult()}
	rsp.Ret.Desc += "facebook login failed"

	// 验证请求参数
	if err := l.validateRequest(req); err != nil {
		entry.Error(err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM, err.Error())
		return rsp, err
	}

	// 调用 SDK 验证 Facebook token
	userInfo, err := sdk.GetUserInfo(&sdk.LoginParams{
		ChannelID:   req.GetChannelId(),
		AccType:     l.LoginType,
		AccessToken: req.GetThirdInfo().GetToken(),
		Code:        req.GetThirdInfo().GetCodeId(),
	})
	if err != nil {
		entry.Errorf("Facebook login: verify token failed: %v", err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}
	if userInfo == nil {
		err = errors.New("user info is nil")
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}
	if req.GetThirdInfo().GetOpenId() != userInfo.OpenID {
		err = errors.New("open id not match")
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_SDK_BAD_INFO, err.Error())
		return rsp, err
	}

	// 获取玩家ID
	playerId, err := rpc_user.GetPlayerIdByOpenId(ctx, req.GetProductId(), req.GetLoginType(), req.GetThirdInfo().GetOpenId())
	if err != nil {
		entry.Errorf("get player id failed: %v", err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, err.Error())
		return rsp, err
	}

	// 如果玩家ID为0，则表示玩家不存在，需要注册
	if playerId == 0 {
		req.IsReg = true
	}

	// 处理登录流程
	return l.DefaultLoginProcess(ctx, req, playerId)
}
