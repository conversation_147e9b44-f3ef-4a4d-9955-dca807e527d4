package logic

import (
	"context"
	"errors"
	"loginsrv/internal/dao"
	"loginsrv/internal/repo/rpc_user"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

// TokenLogin Token登录实现
type TokenLogin struct {
	*BaseLogin
}

// NewTokenLogin 创建Token登录实例
func NewTokenLogin() *TokenLogin {
	return &TokenLogin{
		BaseLogin: NewBaseLogin(commonPB.LOGIN_TYPE_LT_TOKEN),
	}
}

// validateRequest 验证请求参数
func (l *TokenLogin) validateRequest(req *loginRpc.LoginReq) error {
	if err := l.ValidateBaseParams(req); err != nil {
		return err
	}

	accountInfo := req.GetAccountInfo()
	if accountInfo == nil || accountInfo.GetAccount() == "" {
		return errors.New("account info must")
	}

	if req.GetThirdToken() == "" {
		return errors.New("token must")
	}

	return nil
}

// Login Token登录流程
func (l *TokenLogin) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	rsp := &loginRpc.LoginRsp{
		Ret: protox.DefaultResult(),
	}
	rsp.Ret.Desc += "token login failed"

	// 验证请求参数
	if err := l.validateRequest(req); err != nil {
		entry.Error(err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_BAD_PARAM)
		rsp.Ret.Desc += " : " + err.Error()
		return rsp, err
	}

	productId := req.GetProductId()
	accountInfo := req.GetAccountInfo()
	account := accountInfo.GetAccount()
	token := req.GetThirdToken()

	// 根据账号查询玩家信息
	playerId, err := rpc_user.RpcGetPlayerIdByAccount(ctx, productId, account)
	if err != nil {
		entry.Errorf("RPC query account:%+v err:%+v", accountInfo, err)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE)
		rsp.Ret.Desc += " : get account data error"
		return rsp, errors.New("login failed")
	}

	entry.Infof("token login:%+v - playerId : %d token:%s", account, playerId, token)

	// 检查token是否一致
	check, err := dao.DefaultLoginTokenCache.Get(playerId)
	if err != nil {
		entry.Errorf("get token fail:%+v playerId:%+v", err, playerId)
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SYSTEM_MISTAKE, err.Error())
		return rsp, err
	}
	dao.DefaultLoginTokenCache.Flush(playerId)

	if check != token {
		rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_LOGIN_ACCOUNT_PASSWORD)
		return rsp, errors.New("token invalid")
	}

	// Token登录不走默认流程
	// 执行钩子：登录前
	errCode, err := l.Hooks.BeforeLogin(ctx, playerId, req)
	if err != nil {
		rsp = &loginRpc.LoginRsp{Ret: protox.FillCodeResult(errCode, err.Error())}
		return rsp, err
	}

	rsp, err = l.HandleLogin(ctx, req, playerId)
	if err != nil {
		return rsp, err
	}
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)

	// 执行钩子：登录后
	errCode, err = l.Hooks.AfterLogin(ctx, req, rsp)
	if err != nil {
		rsp.Ret = protox.FillCodeResult(errCode, err.Error())
		return rsp, err
	}

	entry.Infof("token login success - playerId:%v", playerId)

	return rsp, nil
}
