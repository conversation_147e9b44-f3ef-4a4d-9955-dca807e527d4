package rpc

import (
	"context"
	"loginsrv/internal/services"

	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc"
)

type LoginServiceServer struct {
}

func (l LoginServiceServer) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	s := services.GetLoginInstance()
	return s.Login(ctx, req)
}

func (l LoginServiceServer) Logout(ctx context.Context, req *loginRpc.LogoutReq) (*loginRpc.LogoutRsp, error) {
	s := services.GetLogoutInstance()
	return s.Logout(ctx, req)
}

func InitLoginRpc() {
	loginRpcService := &LoginServiceServer{}
	loginRpc.RegisterLoginServiceServer(rpc.Server, loginRpcService)
}
