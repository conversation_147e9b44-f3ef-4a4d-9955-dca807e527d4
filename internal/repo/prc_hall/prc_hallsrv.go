package rpc_hall

import (
	"context"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	hallRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/hallrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_hall"
)

func RpcQueryHolidayInfo(ctx context.Context) commonPB.HOLIDAY_TYPE {
	entry := logx.NewLogEntry(ctx)
	rpcCli := crpc_hall.GetHallRpcInstance().GetHallRpcClient()
	if rpcCli == nil {
		entry.Errorf("hall rpc client nil")
		return commonPB.HOLIDAY_TYPE_HT_UNKNOWN
	}

	req := &hallRpc.QueryHolidayTypeReq{}

	rsp, err := rpcCli.QueryHolidayType(ctx, req)
	if err != nil || rsp == nil {
		entry.Errorf("rpc query holiday type failed : %v", err)
		return commonPB.HOLIDAY_TYPE_HT_UNKNOWN
	}

	entry.Debugf("rpc query holiday type success : %v", rsp)

	return rsp.GetType()
}
