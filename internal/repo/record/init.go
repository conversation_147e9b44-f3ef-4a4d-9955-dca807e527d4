package record

import (
	"context"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/panjf2000/ants/v2"
	"github.com/sirupsen/logrus"
	"sync"
)

const workerNum = 10000

// Init 初始化记录模块
func Init() {
	InitProducer()
	InitTaskPool()
}

var (
	producer     event.Sender
	producerOnce sync.Once

	// 通用协程池
	taskPool     *ants.Pool
	taskPoolOnce sync.Once
)

// Task 通用任务接口
type Task func()

// InitProducer 初始化消息生产者
func InitProducer() {
	producerOnce.Do(func() {
		sender, err := event.NewKafkaSender()
		if err != nil {
			logrus.Fatalf("create kafka sender failed: %s", err)
		}
		producer = sender
	})
}

// InitTaskPool 初始化通用任务协程池
func InitTaskPool() {
	taskPoolOnce.Do(func() {
		var err error
		// 非阻塞模式：协程池满时立即返回错误，不阻塞业务
		taskPool, err = ants.NewPool(workerNum, ants.WithNonblocking(true))
		if err != nil {
			logrus.Fatalf("create task pool failed: %s", err)
		}
	})
}

// GetProducer 获取消息生产者实例
func GetProducer() event.Sender {
	return producer
}

// SubmitTask 提交任务到协程池异步执行
func SubmitTask(task Task) {
	if taskPool == nil {
		logrus.Error("task pool not initialized")
		return
	}

	// 协程池满了会报错然后丢弃数据
	err := taskPool.Submit(task)
	if err != nil {
		logrus.Errorf("submit task failed: %v", err)
	}
}

// sendMessage 通用的消息发送函数
func sendMessage(topic string, message event.Event) {
	ctx := context.Background()
	if err := GetProducer().SendWithTopic(ctx, topic, message); err != nil {
		logrus.Errorf("send message failed: %s msg: %+v", err, message)
	}
}

// Release 优雅关闭协程池，等待所有任务完成
func Release() {
	if taskPool != nil {
		logrus.Info("gracefully shutting down task pool...")
		taskPool.Release()
		logrus.Info("task pool shutdown completed")
	}
}
