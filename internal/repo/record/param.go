package record

import loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"

type LoginRecordParam struct {
	Rsp *loginRpc.LoginRsp // 登录回包
	Req *loginRpc.LoginReq // 登录请求数据
}

type LogoutRecordParam struct {
	Req         *loginRpc.LogoutReq // 登出请求数据
	RecentMsgID string              // 最近的消息列表
}

func InitLoginRecordParam(req *loginRpc.LoginReq, rsp *loginRpc.LoginRsp) *LoginRecordParam {
	return &LoginRecordParam{
		Rsp: rsp,
		Req: req,
	}
}
