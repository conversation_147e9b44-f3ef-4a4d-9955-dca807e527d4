package record

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
)

type LoginRecordParam struct {
	Rsp *loginRpc.LoginRsp // 登录回包
	Req *loginRpc.LoginReq // 登录请求数据
}

type LogoutRecordParam struct {
	Req         *loginRpc.LogoutReq // 登出请求数据
	RecentMsgID string              // 最近的消息列表
}

// LoginErrorRecordParam 登录错误记录参数
type LoginErrorRecordParam struct {
	Req       *loginRpc.LoginReq // 登录请求数据
	ErrorCode commonPB.ErrCode   // 错误码
	ErrorMsg  string             // 错误详情
}

func InitLoginRecordParam(req *loginRpc.LoginReq, rsp *loginRpc.LoginRsp) *LoginRecordParam {
	return &LoginRecordParam{
		Rsp: rsp,
		Req: req,
	}
}

// InitLoginErrorRecordParam 初始化登录错误记录参数
func InitLoginErrorRecordParam(req *loginRpc.LoginReq, errorCode commonPB.ErrCode, errorMsg string) *LoginErrorRecordParam {
	return &LoginErrorRecordParam{
		Req:       req,
		ErrorCode: errorCode,
		ErrorMsg:  errorMsg,
	}
}
