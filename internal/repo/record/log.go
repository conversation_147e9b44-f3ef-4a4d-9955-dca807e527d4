package record

import (
	"context"
	"github.com/sirupsen/logrus"
)

// LoginRecord 登录日志记录
func LoginRecord(record *LoginRecordParam) {
	logrus.Debugf("LoginRecord uid:%d product_id:%d", record.Rsp.PlayerId, record.Req.ProductId)

	loginRecord := &RLogin{}
	loginRecord.SendAsync(record)
}

// RegisterRecord 注册流水记录
func RegisterRecord(record *LoginRecordParam) {
	logrus.Debugf("RegisterRecord uid:%d product_id:%d", record.Rsp.PlayerId, record.Req.ProductId)

	registerRecord := &RRegister{}
	registerRecord.SendAsync(record)
}

// LogoutRecord 登出记录
func LogoutRecord(ctx context.Context, record *LogoutRecordParam) {
	logrus.Debugf("LogoutRecord uid:%d", record.Req.PlayerId)

	logoutRecord := &RLogout{}
	logoutRecord.SendAsync(ctx, record)
}
