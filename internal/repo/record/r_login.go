package record

import (
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_login"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/spf13/cast"
)

// RLogin 登录记录
type RLogin struct {
	PlayerId      uint64
	LoginType     commonPB.LOGIN_TYPE // 登录类型
	LastLoginTime int64               // 最后登录时间
	DeviceModel   string
	DeviceBrand   string
	Os            string
	OsLanguage    string
	Resolution    string
	IP            string
	GrayStrategy  commonPB.GRAY_STRATEGY
}

// GetTableName 获取表名
func (r *RLogin) GetTableName() string {
	return "r_login"
}

// Format 格式化数据
func (r *RLogin) Format() string {
	return recordx.MarshalWithLine(
		transform.Uint642Str(r.PlayerId),
		transform.I32ToStr(int32(r.LoginType)),
		transform.Int642Str(r.LastLoginTime),
		r.DeviceModel,
		r.DeviceBrand,
		r.Os,
		r.OsLanguage,
		r.Resolution,
		r.IP)
}

// BuildFromLoginParam 从登录参数构建登录记录
func (r *RLogin) BuildFromLoginParam(param *LoginRecordParam) *RLogin {
	loginTime := timex.Now()

	return &RLogin{
		PlayerId:      param.Rsp.PlayerId,
		LoginType:     param.Req.LoginType,
		LastLoginTime: loginTime.Unix(),
		DeviceModel:   param.Req.DeviceInfo.DeviceModel,
		DeviceBrand:   param.Req.DeviceInfo.DeviceBrand,
		Os:            param.Req.DeviceInfo.Os,
		OsLanguage:    param.Req.DeviceInfo.OsLanguage,
		Resolution:    param.Req.DeviceInfo.Resolution,
		IP:            param.Req.GetDeviceInfo().GetClientIp(),
		GrayStrategy:  param.Rsp.GrayStrategy,
	}
}

// BuildRecordData 构建记录数据
func (r *RLogin) BuildRecordData(param *LoginRecordParam) []byte {
	recordHeader := recordx.DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID(param.Req.ProductId),
		Platform:    param.Rsp.RichUserInfo.Platform,
		AppVersion:  param.Req.ClientVersion,
		AppLanguage: param.Req.DeviceInfo.AppLanguage,
		Country:     param.Rsp.RichUserInfo.BriefUserInfo.Country,
		ChannelType: param.Req.ChannelId,
		AccType:     param.Rsp.RichUserInfo.AccType,
	}

	loginRecord := r.BuildFromLoginParam(param)
	return []byte(recordx.SerializeData(recordHeader, loginRecord))
}

// BuildAnalyzeData 构建分析数据
func (r *RLogin) BuildAnalyzeData(param *LoginRecordParam) []byte {
	loginTime := timex.Now()

	analyzeHeader := analyze.DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID(param.Req.ProductId),
		ChannelType: param.Req.ChannelId,
		Platform:    param.Rsp.RichUserInfo.Platform,
		AppLanguage: param.Req.DeviceInfo.AppLanguage,
		Country:     param.Rsp.RichUserInfo.BriefUserInfo.Country,
		AccType:     param.Rsp.RichUserInfo.AccType,
		AppVersion:  param.Req.ClientVersion,
		PlayerId:    param.Rsp.PlayerId,
		IP:          param.Req.GetDeviceInfo().GetClientIp(),
	}

	loginAnalyze := &a_login.TeLoginInfo{
		DefaultHeader: &analyzeHeader,
		TeLogin: &a_login.TeLogin{
			LoginType:     param.Req.LoginType,
			DeviceInfo:    param.Req.DeviceInfo,
			LastLoginTime: loginTime,
			GrayStrategy:  param.Rsp.GrayStrategy,
		},
	}

	data, _ := json.Marshal(loginAnalyze)
	return data
}

// SendAsync 异步发送登录记录和分析数据
func (r *RLogin) SendAsync(param *LoginRecordParam) {
	playerId := cast.ToString(param.Rsp.PlayerId)

	SubmitTask(func() {
		// 发送记录数据
		recordData := r.BuildRecordData(param)
		recordMessage := event.NewMessage(playerId, recordData)
		sendMessage(r.GetTableName(), recordMessage)

		// 发送分析数据
		analyzeData := r.BuildAnalyzeData(param)
		analyzeMessage := event.NewMessage(playerId, analyzeData)
		sendMessage(new(a_login.TeLoginInfo).GetEvent(), analyzeMessage)
	})
}
