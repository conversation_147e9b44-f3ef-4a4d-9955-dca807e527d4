package record

import (
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_login"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/spf13/cast"
)

// RRegister 注册记录
type RRegister struct {
	PlayerId     uint64
	LoginType    commonPB.LOGIN_TYPE // 登录类型
	DeviceModel  string
	DeviceBrand  string
	Os           string
	OsLanguage   string
	Resolution   string
	AdjustId     string
	Idfa         string
	TimeZone     string
	DeviceName   string
	DeviceCode   string
	Mvno         string
	Android      string
	ThirdToken   string
	BundleName   string
	Name         string
	Avatar       int32
	AvatarUrl    string
	RegisterTime int64
}

// GetTableName 获取表名
func (r *RRegister) GetTableName() string {
	return "r_register"
}

// Format 格式化数据
func (r *RRegister) Format() string {
	return recordx.MarshalWithLine(
		transform.Uint642Str(r.PlayerId),
		transform.Uint642Str(uint64(r.LoginType)),
		r.DeviceModel,
		r.DeviceBrand,
		r.Os,
		r.OsLanguage,
		r.Resolution,
		r.AdjustId,
		r.Idfa,
		r.TimeZone,
		r.DeviceName,
		r.DeviceCode,
		r.Mvno,
		r.Android,
		r.ThirdToken,
		r.BundleName,
		transform.Uint642Str(uint64(r.RegisterTime)),
	)
}

// BuildFromLoginParam 从登录参数构建注册记录
func (r *RRegister) BuildFromLoginParam(param *LoginRecordParam) *RRegister {
	registerTime := timex.Now()

	return &RRegister{
		PlayerId:     param.Rsp.PlayerId,
		LoginType:    param.Req.LoginType,
		DeviceModel:  param.Req.DeviceInfo.DeviceModel,
		DeviceBrand:  param.Req.DeviceInfo.DeviceBrand,
		Os:           param.Req.DeviceInfo.Os,
		OsLanguage:   param.Req.DeviceInfo.OsLanguage,
		Resolution:   param.Req.DeviceInfo.Resolution,
		AdjustId:     param.Req.DeviceInfo.AdjustId,
		Idfa:         param.Req.DeviceInfo.Idfa,
		TimeZone:     param.Req.DeviceInfo.TimeZone,
		DeviceName:   param.Req.DeviceInfo.DeviceName,
		DeviceCode:   param.Req.DeviceInfo.DeviceCode,
		Mvno:         param.Req.DeviceInfo.Mvno,
		Android:      param.Req.DeviceInfo.AdjustId,
		ThirdToken:   param.Req.ThirdToken,
		BundleName:   param.Req.BundleName,
		Name:         param.Rsp.RichUserInfo.BriefUserInfo.Name,
		Avatar:       int32(param.Rsp.RichUserInfo.BriefUserInfo.Avatar),
		AvatarUrl:    param.Rsp.RichUserInfo.BriefUserInfo.AvatarUrl,
		RegisterTime: registerTime.Unix(),
	}
}

// BuildRecordData 构建记录数据
func (r *RRegister) BuildRecordData(param *LoginRecordParam) []byte {
	recordHeader := recordx.DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID(param.Req.ProductId),
		Platform:    param.Rsp.RichUserInfo.Platform,
		AppVersion:  param.Req.ClientVersion,
		AppLanguage: param.Req.DeviceInfo.AppLanguage,
		Country:     param.Rsp.RichUserInfo.BriefUserInfo.Country,
		ChannelType: param.Req.ChannelId,
		AccType:     param.Rsp.RichUserInfo.AccType,
	}

	registerRecord := r.BuildFromLoginParam(param)
	return []byte(recordx.SerializeData(recordHeader, registerRecord))
}

// BuildAnalyzeData 构建分析数据
func (r *RRegister) BuildAnalyzeData(param *LoginRecordParam) []byte {
	registerTime := timex.Now()

	analyzeHeader := analyze.DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID(param.Req.ProductId),
		ChannelType: param.Req.ChannelId,
		Platform:    param.Rsp.RichUserInfo.Platform,
		AppLanguage: param.Req.DeviceInfo.AppLanguage,
		Country:     param.Rsp.RichUserInfo.BriefUserInfo.Country,
		AccType:     param.Rsp.RichUserInfo.AccType,
		AppVersion:  param.Req.ClientVersion,
		PlayerId:    param.Rsp.PlayerId,
		IP:          param.Req.GetDeviceInfo().GetClientIp(),
	}

	registerAnalyze := &a_login.TeRegisterInfo{
		DefaultHeader: &analyzeHeader,
		TeRegister: &a_login.TeRegister{
			DeviceInfo:   param.Req.DeviceInfo,
			RegisterTime: registerTime,
		},
	}

	data, _ := json.Marshal(registerAnalyze)
	return data
}

// SendAsync 异步发送注册记录和分析数据
func (r *RRegister) SendAsync(param *LoginRecordParam) {
	playerId := cast.ToString(param.Rsp.PlayerId)

	// 在同一个任务中发送记录数据和分析数据
	SubmitTask(func() {
		// 发送记录数据
		recordData := r.BuildRecordData(param)
		recordMessage := event.NewMessage(playerId, recordData)
		sendMessage(r.GetTableName(), recordMessage)

		// 发送分析数据
		analyzeData := r.BuildAnalyzeData(param)
		analyzeMessage := event.NewMessage(playerId, analyzeData)
		sendMessage(new(a_login.TeRegisterInfo).GetEvent(), analyzeMessage)
	})
}
