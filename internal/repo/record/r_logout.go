package record

import (
	"context"
	"encoding/json"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_login"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"github.com/spf13/cast"
	"time"
)

// RLogout 登出记录（只有分析数据）
type RLogout struct{}

// BuildAnalyzeData 构建登出分析数据
func (r *RLogout) BuildAnalyzeData(ctx context.Context, param *LogoutRecordParam) []byte {
	analyzeHeader := analyze.NewTeDefaultHeaderFromCtx(ctx)

	logoutAnalyze := &a_login.TeLogoutInfo{
		DefaultHeader: analyzeHeader,
		TeLogout: &a_login.TeLogout{
			LogoutTime:  time.Now(),
			RecentMsgID: param.RecentMsgID,
		},
	}

	data, _ := json.Marshal(logoutAnalyze)
	return data
}

// SendAsync 异步发送登出分析数据
func (r *RLogout) SendAsync(ctx context.Context, param *LogoutRecordParam) {
	playerId := cast.ToString(param.Req.PlayerId)

	SubmitTask(func() {
		analyzeData := r.BuildAnalyzeData(ctx, param)
		analyzeMessage := event.NewMessage(playerId, analyzeData)
		sendMessage(new(a_login.TeLogoutInfo).GetEvent(), analyzeMessage)
	})
}
