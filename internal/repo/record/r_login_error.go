package record

import (
	"encoding/json"
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/analyze/a_login"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/recordx"
	"git.keepfancy.xyz/back-end/frameworks/kit/kafka/event"
	"git.keepfancy.xyz/back-end/frameworks/lib/timex"
	"git.keepfancy.xyz/back-end/frameworks/lib/transform"
	"github.com/spf13/cast"
)

// RLoginError 登录错误记录
type RLoginError struct {
	LoginType   commonPB.LOGIN_TYPE // 登录类型
	ErrorCode   commonPB.ErrCode    // 错误码
	ErrorMsg    string              // 错误详情
	ErrorTime   int64               // 错误时间
	DeviceModel string              // 设备型号
	DeviceBrand string              // 设备品牌
	Os          string              // 操作系统
	OsLanguage  string              // 系统语言
	Resolution  string              // 分辨率
	IP          string              // IP地址
}

// GetTableName 获取表名
func (r *RLoginError) GetTableName() string {
	return "r_login_error"
}

// Format 格式化数据
func (r *RLoginError) Format() string {
	return recordx.MarshalWithLine(
		transform.I32ToStr(int32(r.LoginType)),
		transform.I32ToStr(int32(r.ErrorCode)),
		r.ErrorMsg,
		transform.Int642Str(r.ErrorTime),
		r.DeviceModel,
		r.DeviceBrand,
		r.Os,
		r.OsLanguage,
		r.Resolution,
		r.IP)
}

// BuildFromLoginErrorParam 从登录错误参数构建登录错误记录
func (r *RLoginError) BuildFromLoginErrorParam(param *LoginErrorRecordParam) *RLoginError {
	errorTime := timex.Now()

	return &RLoginError{
		LoginType:   param.Req.LoginType,
		ErrorCode:   param.ErrorCode,
		ErrorMsg:    param.ErrorMsg,
		ErrorTime:   errorTime.Unix(),
		DeviceModel: param.Req.DeviceInfo.DeviceModel,
		DeviceBrand: param.Req.DeviceInfo.DeviceBrand,
		Os:          param.Req.DeviceInfo.Os,
		OsLanguage:  param.Req.DeviceInfo.OsLanguage,
		Resolution:  param.Req.DeviceInfo.Resolution,
		IP:          param.Req.GetDeviceInfo().GetClientIp(),
	}
}

// BuildRecordData 构建记录数据
func (r *RLoginError) BuildRecordData(param *LoginErrorRecordParam) []byte {
	recordHeader := recordx.DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID(param.Req.ProductId),
		Platform:    commonPB.PLATFORM_TYPE_UNKNOWN, // 错误情况下可能没有平台信息
		AppVersion:  param.Req.ClientVersion,
		AppLanguage: param.Req.DeviceInfo.AppLanguage,
		Country:     "", // 错误情况下可能没有国家信息
		ChannelType: param.Req.ChannelId,
		AccType:     commonPB.ACC_TYPE_UNKNOWN, // 错误情况下可能没有账号类型信息
	}

	loginErrorRecord := r.BuildFromLoginErrorParam(param)
	return []byte(recordx.SerializeData(recordHeader, loginErrorRecord))
}

// BuildAnalyzeData 构建分析数据
func (r *RLoginError) BuildAnalyzeData(param *LoginErrorRecordParam) []byte {
	errorTime := timex.Now()

	analyzeHeader := analyze.DefaultHeader{
		ProductID:   commonPB.PRODUCT_ID(param.Req.ProductId),
		ChannelType: param.Req.ChannelId,
		Platform:    commonPB.PLATFORM_TYPE_UNKNOWN, // 错误情况下可能没有平台信息
		AppLanguage: param.Req.DeviceInfo.AppLanguage,
		Country:     "", // 错误情况下可能没有国家信息
		AccType:     commonPB.ACC_TYPE_UNKNOWN, // 错误情况下可能没有账号类型信息
		AppVersion:  param.Req.ClientVersion,
		PlayerId:    0, // 错误情况下可能没有玩家ID
		IP:          param.Req.GetDeviceInfo().GetClientIp(),
	}

	// 使用登录分析结构，但标记为错误
	loginErrorAnalyze := &a_login.TeLoginInfo{
		DefaultHeader: &analyzeHeader,
		TeLogin: &a_login.TeLogin{
			LoginType:     param.Req.LoginType,
			DeviceInfo:    param.Req.DeviceInfo,
			LastLoginTime: errorTime,
			GrayStrategy:  commonPB.GRAY_STRATEGY_UNKNOWN,
		},
	}

	data, _ := json.Marshal(loginErrorAnalyze)
	return data
}

// SendAsync 异步发送登录错误记录和分析数据
func (r *RLoginError) SendAsync(param *LoginErrorRecordParam) {
	// 使用错误码作为消息key，便于分区和查询
	messageKey := cast.ToString(int32(param.ErrorCode))

	SubmitTask(func() {
		// 发送记录数据
		recordData := r.BuildRecordData(param)
		recordMessage := event.NewMessage(messageKey, recordData)
		sendMessage(r.GetTableName(), recordMessage)

		// 发送分析数据 - 使用特殊的错误事件主题
		analyzeData := r.BuildAnalyzeData(param)
		analyzeMessage := event.NewMessage(messageKey, analyzeData)
		sendMessage("login_error_analyze", analyzeMessage)
	})
}
