package services

import (
	"context"
	"loginsrv/internal/logic"
	"loginsrv/internal/monitor"
	"loginsrv/internal/repo/record"
	"sync"

	"git.keepfancy.xyz/back-end/frameworks/lib/transform"

	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	userRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/userrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/rpc_biz/crpc_user"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
)

type LoginLogic struct {
	ctx context.Context
}

var (
	onceLogin              = &sync.Once{}
	singletonLoginInstance *LoginLogic
)

func GetLoginInstance() *LoginLogic {
	if singletonLoginInstance != nil {
		return singletonLoginInstance
	}

	onceLogin.Do(func() {
		singletonLoginInstance = &LoginLogic{}
	})
	return singletonLoginInstance
}

func (l *LoginLogic) Login(ctx context.Context, req *loginRpc.LoginReq) (*loginRpc.LoginRsp, error) {
	entry := logx.NewLogEntry(ctx)
	entry.Infof("登录请求[LoginReq: %s]", req.String())

	rsp := &loginRpc.LoginRsp{
		Ret: protox.DefaultResult(),
	}

	// 获取登录实现
	iLogin := logic.NewLoginImpl(req.LoginType)
	if iLogin == nil {
		rsp.Ret.Code = commonPB.ErrCode_ERR_LOGIN_TYPE
		return rsp, nil
	}

	var errLogin error
	rsp, errLogin = iLogin.Login(ctx, req)
	if errLogin != nil {
		// 如果rsp的有错误码则直接返回
		if rsp.GetRet().GetCode() != commonPB.ErrCode_ERR_SUCCESS {
			return rsp, nil
		}
		return rsp, errLogin
	}

	// TODO 封号逻辑判断，从用户表获取是否封号

	// TODO RPC 更新玩家信息：登录TYPE、DeviceCode、版本号、操作系统等信息
	updateLogin := &userRpc.UpdateLoginReq{
		ProductId:  req.ProductId,
		PlayerId:   rsp.PlayerId,
		DeviceInfo: req.GetDeviceInfo(),
	}
	crpc_user.GetUserRpcInstance().GetUserRpcClient().UpdatePlayerLogin(ctx, updateLogin)

	// TODO 投递到消息总线广播

	// 投递流水记录 这里使用深拷贝
	data := record.InitLoginRecordParam(req, rsp)

	if rsp.IsReg {
		// 注册记录
		record.RegisterRecord(data)
		monitor.RegisterUserTotal.WithLabelValues(transform.I32ToStr(req.ProductId), transform.Uint642Str(rsp.PlayerId)).Inc()
	} else {
		monitor.DayLoginUserTotal.WithLabelValues(transform.I32ToStr(req.ProductId), transform.Uint642Str(rsp.PlayerId)).Inc()
	}

	// 不管登录注册都去投递一份登录流水
	record.LoginRecord(data)

	//pubsub.PublishLoginEvent(ctx, rsp.PlayerId, req.LoginType)

	return rsp, nil
}
