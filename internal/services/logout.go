package services

import (
	"context"
	"errors"
	"loginsrv/internal/dao"

	"sync"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/protox"
	"github.com/sirupsen/logrus"
)

type LogoutLogic struct {
	ctx context.Context
}

var (
	onceLogout              = &sync.Once{}
	singletonLogoutInstance *LogoutLogic
)

func GetLogoutInstance() *LogoutLogic {
	if singletonLogoutInstance != nil {
		return singletonLogoutInstance
	}

	onceLogout.Do(func() {
		singletonLogoutInstance = &LogoutLogic{}
	})
	return singletonLogoutInstance
}

func (l *LogoutLogic) Logout(ctx context.Context, req *loginRpc.LogoutReq) (*loginRpc.LogoutRsp, error) {
	logrus.Infof("登出请求[LogoutReq: %s]", req.String())

	rsp := &loginRpc.LogoutRsp{
		Ret: protox.DefaultResult(),
	}

	playerID := req.GetPlayerId()

	// 删除token session
	if err := dao.DefaultLoginTokenCache.Delete(playerID); err != nil {
		return rsp, errors.New(commonPB.ErrCode_ERR_SYSTEM_MISTAKE.String())
	}

	// record.LogoutRecord(ctx, &record.LogoutRecordParam{Req: *req})
	rsp.Ret = protox.FillCodeResult(commonPB.ErrCode_ERR_SUCCESS)
	// pubsub.PublishLogoutEvent()

	return rsp, nil
}
