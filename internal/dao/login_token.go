package dao

import (
	"errors"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/dictionary/dict_redis"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/redisfactory"

	"strconv"
	"strings"
	"time"

	"github.com/gomodule/redigo/redis"
	"github.com/sirupsen/logrus"
)

var DefaultLoginTokenCache *TokenCache

const (
	// cacheTimeout = 3600 * 1 // for test
	cacheTimeout = 3600 * 24 * 30 // 缓存失效时间改成30天
)

var (
	ErrTokenGetFailed    = errors.New("token get failed")
	ErrTokenSetFailed    = errors.New("token set failed")
	ErrTokenExpireFailed = errors.New("token expire failed")
	ErrTokenNotFound     = errors.New("token not found")
	ErrTokenDelFailed    = errors.New("token del failed")
)

type TokenCache struct {
	pool *redis.Pool
}

func NewLoginTokenSessionCache(redisSource string) *TokenCache {
	cfg := strings.Split(redisSource, "@")
	if len(cfg) != 2 {
		logrus.Panicf("redis服务地址配置错误")
	}

	pool := &redis.Pool{
		MaxIdle:         300,        // 最多可以保持多少空闲连接, 设置为MaxActive的20%～50%
		MaxActive:       1000 * 1.5, // 最多可以有多少个活跃连接，设置为并发请求的1.5倍左右
		IdleTimeout:     240 * time.Second,
		MaxConnLifetime: 300 * time.Second,
		Dial:            func() (redis.Conn, error) { return redis.Dial("tcp", cfg[0], redis.DialPassword(cfg[1])) },
	}

	return &TokenCache{
		pool: pool,
	}
}

func (s *TokenCache) getRedisKey(playerID uint64) string {
	return strings.Join([]string{"services:token", strconv.FormatUint(playerID, 10)}, dict.SysSymbolColon)
}

func (s *TokenCache) Create(playerID uint64, token string) error {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(playerID)
	err := c.Send(dict.ConfigRedisSETEX, key, cacheTimeout, token)
	if err != nil {
		return errors.Join(ErrTokenSetFailed, err)
	}

	return nil
}

func (s *TokenCache) Get(playerID uint64) (string, error) {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(playerID)
	token, err := redis.String(c.Do(dict.ConfigRedisGET, key))
	if err != nil {
		if err == redis.ErrNil {
			return "", ErrTokenNotFound
		}
		return "", ErrTokenGetFailed
	}

	logrus.Debugf("Token: [%d:%s]", playerID, token)
	return token, nil
}

func (s *TokenCache) Set(playerID uint64, token string) error {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(playerID)
	err := c.Send(dict.ConfigRedisSETEX, key, cacheTimeout, token)
	if err != nil {
		return ErrTokenSetFailed
	}

	return nil
}

func (s *TokenCache) Flush(playerID uint64) error {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(playerID)
	err := c.Send(dict.ConfigRedisEXPIRE, key, cacheTimeout)
	if err != nil {
		return ErrTokenExpireFailed
	}

	return nil
}

func (s *TokenCache) Delete(playerID uint64) error {
	c := s.pool.Get()
	defer c.Close()

	key := s.getRedisKey(playerID)
	err := c.Send(dict.ConfigRedisDEL, key)
	if err != nil {
		return ErrTokenDelFailed
	}

	return nil
}

func InitTokenRedisInstance() {
	playRedis := redisfactory.DefaultFactory.GetRedisConfig(dict_redis.RDBPlayer)
	redisSource := playRedis.Address + "@" + playRedis.Passwd
	DefaultLoginTokenCache = NewLoginTokenSessionCache(redisSource)
}
