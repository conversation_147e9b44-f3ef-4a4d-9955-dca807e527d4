package dao

import (
	"testing"

	"github.com/gomodule/redigo/redis"
)

func init() {
	DefaultLoginTokenCache = NewLoginTokenSessionCache("192.168.1.54:6379@8888")
}

func TestTokenCache_Create(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		playerID uint64
		token    string
	}
	var tests []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if err := s.Create(tt.args.playerID, tt.args.token); (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTokenCache_Delete(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		playerID uint64
	}
	var tests []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if err := s.Delete(tt.args.playerID); (err != nil) != tt.wantErr {
				t.Errorf("Delete() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTokenCache_Flush(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		playerID uint64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if err := s.Flush(tt.args.playerID); (err != nil) != tt.wantErr {
				t.Errorf("Flush() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTokenCache_Get(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		playerID uint64
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			got, err := s.Get(tt.args.playerID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Get() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Get() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTokenCache_Set(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		playerID uint64
		token    string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if err := s.Set(tt.args.playerID, tt.args.token); (err != nil) != tt.wantErr {
				t.Errorf("Set() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestTokenCache_getRedisKey(t *testing.T) {
	type fields struct {
		pool *redis.Pool
	}
	type args struct {
		playerID uint64
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   string
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &TokenCache{
				pool: tt.fields.pool,
			}
			if got := s.getRedisKey(tt.args.playerID); got != tt.want {
				t.Errorf("getRedisKey() = %v, want %v", got, tt.want)
			}
		})
	}
}
