package pubsub

import (
	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	loginRpc "git.keepfancy.xyz/back-end/fancy-common/pkg/intranetrpc/loginrpc"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/mq"
	"git.keepfancy.xyz/back-end/frameworks/kit/rpc/interceptor"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
	"loginsrv/internal/repo/record"
)

const (
	ConsumerChannelLoginSrv = "consumer_channel_login_broker"
)

// InitSubscribe 初始化订阅
func InitSubscribe() {
	err := mq.SubscribeEvent(commonPB.EVENT_TYPE_ET_LOGOUT.String(), ConsumerChannelLoginSrv, HandleLogoutEvent)
	if err != nil {
		logrus.Errorf("HandleLoginEvent订阅失败: %v", err)
		return
	}
}

func HandleLogoutEvent(body []byte) {
	msg := &commonPB.EventCommon{}
	if err := proto.Unmarshal(body, msg); err != nil {
		logrus.Errorf("HandleLogoutEvent反序列化失败: %v", err)
		return
	}
	// TODO 消费登录mq
	logrus.Infof("HandleLoginEvent:%v", msg)

	ctx := interceptor.NewRpcClientCtx(
		interceptor.WithPlayerId(msg.PlayerId),
		interceptor.WithProductId(msg.ProductId),
		interceptor.WithChannelType(msg.ChannelId),
	)
	recentMsgID, ok := msg.StrData[int32(commonPB.EVENT_STR_KEY_ESK_LOGOUT_RECENT_MSG_IDS)]
	if !ok {
		recentMsgID = ""
	}
	record.LogoutRecord(ctx, &record.LogoutRecordParam{Req: &loginRpc.LogoutReq{PlayerId: msg.PlayerId}, RecentMsgID: recentMsgID})
}
