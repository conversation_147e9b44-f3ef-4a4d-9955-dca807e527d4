package pubsub

import (
	"context"
	"sync"
	"testing"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/frameworks/dict"
	"git.keepfancy.xyz/back-end/frameworks/kit/nsqx"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
	"github.com/spf13/viper"
)

func init() {
	viper.Set(dict.ConfigNsqDAddr, "************:4150")
	viper.Set(dict.ConfigNsqHttpAddr, "************:4151")
	viper.Set(dict.ConfigNsqLookUpdAddress, "************:4161")
	nsqx.Setup()
}

func TestPubLogin(t *testing.T) {
	PublishLoginEvent(context.TODO(), 111, commonPB.LOGIN_TYPE_LT_VISITOR)
}
func TestPubLogout(t *testing.T) {
	PublishLogoutEvent(111)
}

func TestPubAndSub(t *testing.T) {

	InitSubscribe()

	wg := sync.WaitGroup{}
	wg.Add(1)
	safego.Go(func() {
		for i := 0; i < 1000; i++ {
			PublishLoginEvent(context.Background(), 111, commonPB.LOGIN_TYPE_LT_VISITOR)
		}
	})
}
