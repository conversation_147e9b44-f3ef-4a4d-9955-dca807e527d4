package config

import (
	"git.keepfancy.xyz/back-end/fancy-common/pkg/cmodel"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/config"
)

func InitConfig() error {
	serviceConfig := config.NewServiceConfig()

	serviceConfig.Register("InitRealNameAuthCfg", cmodel.InitRealNameAuthCfg)   // 初始化实名认证配置
	serviceConfig.Register("InitBlockStrategyCfg", cmodel.InitBlockStrategyCfg) // 封杀策略配置
	serviceConfig.Register("InitWhiteListCfg", cmodel.InitWhiteListCfg)         // 初始化白名单配置
	serviceConfig.Register("InitLocationBlockCfg", cmodel.InitLocationBlockCfg) // 初始化位置封杀配置
	serviceConfig.Register("InitGrayStrategyCfg", cmodel.InitGrayStrategyCfg)   // 初始化灰度策略配置
	serviceConfig.Register("InitAppUpdateInfoCfg", cmodel.InitAppUpdateInfoCfg) // 维护更新配置

	return serviceConfig.ExecuteAll()
}
